package types

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestJSONStringArray_AddPrefixSmart(t *testing.T) {
	tests := []struct {
		name     string
		input    JSONStringArray
		expected JSONStringArray
	}{
		{
			name:     "完整URL不变",
			input:    JSONStringArray{"https://example.com/image.jpg", "http://test.com/file.png"},
			expected: JSONStringArray{"https://example.com/image.jpg", "http://test.com/file.png"},
		},
		{
			name:     "rbcare路径使用旧前缀",
			input:    JSONStringArray{"rbcare/images/test.jpg", "rbcare/files/doc.pdf"},
			expected: JSONStringArray{"https://dt1.readboy.com/rbcare/images/test.jpg", "https://dt1.readboy.com/rbcare/files/doc.pdf"},
		},
		{
			name:     "普通相对路径使用新前缀",
			input:    JSONStringArray{"marketing/images/test.jpg", "uploads/file.png"},
			expected: JSONStringArray{"https://static.readboy.com/marketing/images/test.jpg", "https://static.readboy.com/uploads/file.png"},
		},
		{
			name:     "混合路径",
			input:    JSONStringArray{"https://example.com/image.jpg", "rbcare/old.jpg", "marketing/new.jpg"},
			expected: JSONStringArray{"https://example.com/image.jpg", "https://dt1.readboy.com/rbcare/old.jpg", "https://static.readboy.com/marketing/new.jpg"},
		},
		{
			name:     "空字符串",
			input:    JSONStringArray{"", "marketing/test.jpg", ""},
			expected: JSONStringArray{"", "https://static.readboy.com/marketing/test.jpg", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 复制输入以避免修改原始数据
			result := make(JSONStringArray, len(tt.input))
			copy(result, tt.input)
			
			// 执行智能前缀添加
			result.AddPrefixSmart()
			
			// 验证结果
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAddPrefixIfNeeded(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		prefix   string
		expected string
	}{
		{
			name:     "完整HTTPS URL",
			input:    "https://example.com/image.jpg",
			prefix:   "https://test.com/",
			expected: "https://example.com/image.jpg",
		},
		{
			name:     "完整HTTP URL",
			input:    "http://example.com/image.jpg",
			prefix:   "https://test.com/",
			expected: "http://example.com/image.jpg",
		},
		{
			name:     "相对路径",
			input:    "marketing/images/test.jpg",
			prefix:   "https://test.com/",
			expected: "https://test.com/marketing/images/test.jpg",
		},
		{
			name:     "空字符串",
			input:    "",
			prefix:   "https://test.com/",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := addPrefixIfNeeded(tt.input, tt.prefix)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// 注意：现在我们使用 utils.AddPrefix 来实现智能前缀功能
// 这个测试验证 AddPrefixSmart 方法正确调用了 utils.AddPrefix
