package operation

import (
	"errors"
	"marketing/internal/api/operation"
	"marketing/internal/dao"
	articleDao "marketing/internal/dao/operation"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

type OpArticleSvcInterface interface {
	GetOpArticleList(c *gin.Context, req operation.ReqOpArticleParam) (infos []*operation.OpArticleInfo, total int64)
	DeleteOpArticle(c *gin.Context, id int) error
	EditOpArticle(c *gin.Context, req operation.ReqCreateArticleParam) error
	CheckUserPublisher(c *gin.Context, uid uint, pid uint) error
	UpdateShareable(c *gin.Context, id int, shareable uint8) error
	UpdateEnabled(c *gin.Context, id int, enabled uint8) error
	UpdateTop(c *gin.Context, id int, top uint8) error
}

type OpArticleService struct {
	opArticleCategoryRepo articleDao.OpArticleCategoryDao
	opArticleTagRepo      articleDao.OpArticleTagDao
	shareInfoRepo         dao.ShareInfoDao
	opLikeDao             articleDao.OpLikeDao
	opViewDao             articleDao.OpViewDao
	opCommentDao          articleDao.OpArticleCommentDao
	opArticleRepo         articleDao.OpArticleDao
}

func NewOpArticleService(
	opArticleCategoryRepo articleDao.OpArticleCategoryDao,
	opArticleTagRepo articleDao.OpArticleTagDao,
	shareInfoRepo dao.ShareInfoDao,
	opLikeDao articleDao.OpLikeDao,
	opViewDao articleDao.OpViewDao,
	opCommentDao articleDao.OpArticleCommentDao,
	opArticleRepo articleDao.OpArticleDao) OpArticleSvcInterface {

	return &OpArticleService{
		opArticleCategoryRepo: opArticleCategoryRepo,
		opArticleTagRepo:      opArticleTagRepo,
		shareInfoRepo:         shareInfoRepo,
		opLikeDao:             opLikeDao,
		opViewDao:             opViewDao,
		opCommentDao:          opCommentDao,
		opArticleRepo:         opArticleRepo,
	}
}

func (s *OpArticleService) GetOpArticleList(c *gin.Context, req operation.ReqOpArticleParam) (infos []*operation.OpArticleInfo, total int64) {
	infos, total = s.opArticleRepo.GetOpArticleList(c, req)

	aIds := make([]uint, 0)
	for _, info := range infos {
		aIds = append(aIds, info.ID)
	}

	// 获取标签信息
	tags := s.opArticleTagRepo.GetOpArticleTagsByAids(c, aIds)

	// 计算分享数
	shareList := s.shareInfoRepo.GetOpArticleShareInfos(c, aIds)

	// 计算点赞数
	likeList := s.opLikeDao.GetOpArticleLikeInfos(c, aIds, operation.LikeTargetArticle)

	// 计算浏览数
	viewList := s.opViewDao.GetOpArticleViewInfos(c, aIds, operation.ViewTargetArticle)

	// 计算评论数
	commentList := s.opCommentDao.GetOpArticleCommentInfos(c, aIds)

	for _, info := range infos {
		info.Tags = make([]*operation.OpArticleTagInfo, 0)

		for _, tag := range tags {
			if info.ID == tag.Id {
				info.Tags = append(info.Tags, &operation.OpArticleTagInfo{
					Id:   tag.TagId,
					Name: tag.TagName,
				})
			}
		}

		for _, share := range shareList {
			if info.ID == share.Id {
				info.NumShares = share.Num
			}
		}

		for _, like := range likeList {
			if info.ID == like.Id {
				info.NumLikes = like.Num
			}
		}

		for _, view := range viewList {
			if info.ID == view.Id {
				info.NumViews = view.Num
			}
		}

		for _, comment := range commentList {
			if info.ID == comment.Id {
				info.NumComments = comment.Num
			}
		}
	}

	return
}

func (s *OpArticleService) DeleteOpArticle(c *gin.Context, id int) error {
	return s.opArticleRepo.DeleteOpArticle(c, id)
}

func (s *OpArticleService) EditOpArticle(c *gin.Context, req operation.ReqCreateArticleParam) error {
	if s.opArticleCategoryRepo.GetArticleCategoryById(c, int(req.CategoryID)) == nil {
		return errors.New("编辑文章:分类不存在")
	}

	// 置顶限制：每个大分类下置顶不超过2条
	if req.Top == 1 {
		// 获取目标大分类ID
		mainCid, err := s.opArticleRepo.GetMainCategoryIDByCategoryID(c, req.CategoryID)
		if err != nil {
			return err
		}

		// 如果是更新，则允许在以下情况下跳过计数校验：
		// 1) 原先就是置顶且仍在相同大分类下
		if req.ID != 0 {
			curCid, curTop, err2 := s.opArticleRepo.GetArticleBasic(c, req.ID)
			if err2 != nil {
				return err2
			}
			curMainCid, err3 := s.opArticleRepo.GetMainCategoryIDByCategoryID(c, curCid)
			if err3 != nil {
				return err3
			}
			if curTop == 1 && curMainCid == mainCid {
				// 已经计入该大类置顶，不需要再检查
				if req.ID == 0 {
					// 不会走到这里，占位
				}
			} else {
				count, err4 := s.opArticleRepo.CountTopArticlesByMainCategory(c, mainCid)
				if err4 != nil {
					return err4
				}
				if count >= 2 {
					return errors.New("每个大分类下的置顶条数不超过2条")
				}
			}
		} else {
			// 创建时直接校验
			count, err2 := s.opArticleRepo.CountTopArticlesByMainCategory(c, mainCid)
			if err2 != nil {
				return err2
			}
			if count >= 2 {
				return errors.New("每个大分类下的置顶条数不超过2条")
			}
		}
	}

	if req.ID == 0 {
		return s.opArticleRepo.CreateOpArticle(c, req)
	} else {
		return s.opArticleRepo.UpdateOpArticle(c, req)
	}
}

func (s *OpArticleService) CheckUserPublisher(c *gin.Context, uid uint, pid uint) error {
	roles, err := s.opArticleRepo.GetUserRoles(c, uid)
	if err != nil {
		return err
	}

	publisherIds, err := s.opArticleRepo.GetUserPublisherIds(c, uid, roles)
	if err != nil {
		return err
	}

	if len(publisherIds) == 0 {
		return errors.New("未关联运营号,不能发表内容")
	}

	if !lo.Contains(publisherIds, pid) {
		return errors.New("非账号关联运营号,不能发表内容")
	}
	return nil
}

// UpdateShareable 更新文章分享状态
func (s *OpArticleService) UpdateShareable(c *gin.Context, id int, shareable uint8) error {
	return s.opArticleRepo.UpdateShareable(c, id, shareable)
}

// UpdateEnabled 更新文章发布状态
func (s *OpArticleService) UpdateEnabled(c *gin.Context, id int, enabled uint8) error {
	return s.opArticleRepo.UpdateEnabled(c, id, enabled)
}

// UpdateTop 更新文章置顶状态
func (s *OpArticleService) UpdateTop(c *gin.Context, id int, top uint8) error {
	// 若设置为置顶，需检查所属大分类下置顶数量
	if top == 1 {
		cid, curTop, err := s.opArticleRepo.GetArticleBasic(c, uint(id))
		if err != nil {
			return err
		}
		// 已经置顶则直接返回成功（不改变计数）
		if curTop == 1 {
			return nil
		}
		mainCid, err2 := s.opArticleRepo.GetMainCategoryIDByCategoryID(c, cid)
		if err2 != nil {
			return err2
		}
		count, err3 := s.opArticleRepo.CountTopArticlesByMainCategory(c, mainCid)
		if err3 != nil {
			return err3
		}
		if count >= 2 {
			return errors.New("每个大分类下的置顶条数不超过2条")
		}
	}
	return s.opArticleRepo.UpdateTop(c, id, top)
}
