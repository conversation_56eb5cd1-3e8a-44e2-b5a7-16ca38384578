package endpoint

import (
	api "marketing/internal/api/endpoint"
	"marketing/internal/consts"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ImageInterface interface {
	Lists(c *gin.Context)
	Audit(c *gin.Context)
}

type endpointImage struct {
	svc service.ImageService
}

func NewEndpointImage(svc service.ImageService) ImageInterface {
	return &endpointImage{
		svc: svc,
	}
}

// Lists 获取所有 Endpoint
func (e *endpointImage) Lists(c *gin.Context) {
	var param api.GetEndpointImageReq
	// 绑定请求体中的 JSON 数据
	if err := c.ShouldBind(&param); err != nil {
		handler.Error(c, err)
		return
	}
	//默认分页
	if param.Page == 0 {
		param.Page = consts.DefaultPage
	}
	if param.PageSize == 0 {
		param.PageSize = consts.DefaultPageSize
	}
	//调用 Service 层获取所有 Endpoint 数据
	endpointImages, total, err := e.svc.Lists(c, &param)
	if err != nil {
		// 如果查询失败，返回 500 错误
		handler.Error(c, err)
		return
	}
	handler.Success(c, gin.H{
		"data":      endpointImages,
		"total":     total,
		"page":      param.Page,
		"page_size": param.PageSize,
	})
}

// Audit 终端形象审核
func (e *endpointImage) Audit(c *gin.Context) {
	var req api.AuditImageReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id不能为空"))
		return
	}
	req.ID = id
	err := e.svc.Audit(c, req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}
