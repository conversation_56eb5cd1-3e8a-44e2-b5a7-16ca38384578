package operation

import (
	"marketing/internal/api/operation"
	"marketing/internal/handler"
	"marketing/internal/pkg/e"
	error "marketing/internal/pkg/errors"
	operationSvc "marketing/internal/service/operation"

	"github.com/gin-gonic/gin"
)

type OpArticle struct {
	svc operationSvc.OpArticleSvcInterface
}

func NewOpArticle(svc operationSvc.OpArticleSvcInterface) *OpArticle {
	return &OpArticle{
		svc: svc,
	}
}

func (o *OpArticle) GetOpArticleList(c *gin.Context) {
	list, total := o.svc.GetOpArticleList(c, operation.ReqOpArticleParam{
		Keyword:    e.ReqParamStr(c, "keyword"),
		Enabled:    e.ReqParamInt(c, "enabled", -1),
		Top:        e.ReqParamInt(c, "top", -1),
		CreatorID:  uint(e.ReqParamInt(c, "creator_id")),
		CategoryID: uint(e.ReqParamInt(c, "category_id")),
		OrderBy:    e.ReqParamStr(c, "order_by", "created_at"),
		Order:      e.ReqParamStr(c, "order", "desc"),
		PageNum:    e.ReqParamInt(c, "page_num"),
		PageSize:   e.ReqParamInt(c, "page_size"),
	})

	handler.Success(c, gin.H{
		"list":  list,
		"total": total,
	})
}

func (o *OpArticle) EditOpArticle(c *gin.Context) {
	var req operation.ReqCreateArticleParam
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	// 设置创建者ID
	req.CreatedBy = c.GetUint("uid")

	if err := o.svc.CheckUserPublisher(c, req.CreatedBy, req.PublisherID); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	err := o.svc.EditOpArticle(c, req)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

func (o *OpArticle) DeleteOpArticle(c *gin.Context) {
	err := o.svc.DeleteOpArticle(c, e.ReqParamInt(c, "id"))
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

// UpdateShareable 更新文章分享状态
func (o *OpArticle) UpdateShareable(c *gin.Context) {
	type Request struct {
		Shareable int  `json:"shareable" form:"shareable" binding:"oneof=0 1"`
		ID        uint `json:"id" form:"id" binding:"required"`
	}
	var req Request
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}
	id := int(req.ID)
	shareable := uint8(req.Shareable)

	if id == 0 {
		handler.Error(c, error.NewErr("ID 不能为空"))
		return
	}

	err := o.svc.UpdateShareable(c, id, shareable)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

// UpdateEnabled 更新文章发布状态
func (o *OpArticle) UpdateEnabled(c *gin.Context) {
	type Request struct {
		Enabled int  `json:"enabled" form:"enabled" binding:"oneof=0 1"`
		ID      uint `json:"id" form:"id" binding:"required"`
	}
	var req Request
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}
	id := int(req.ID)
	enabled := uint8(req.Enabled)

	if id == 0 {
		handler.Error(c, error.NewErr("ID 不能为空"))
		return
	}

	err := o.svc.UpdateEnabled(c, id, enabled)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}

// UpdateTop 更新文章置顶状态
func (o *OpArticle) UpdateTop(c *gin.Context) {
	type Request struct {
		Top int  `json:"top" form:"top" binding:"oneof=0 1"`
		ID  uint `json:"id" form:"id" binding:"required"`
	}
	var req Request
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}
	id := int(req.ID)
	top := uint8(req.Top)

	if id == 0 {
		handler.Error(c, error.NewErr("ID 不能为空"))
		return
	}

	err := o.svc.UpdateTop(c, id, top)
	if err != nil {
		handler.Error(c, error.NewErr(err.Error()))
		return
	}

	handler.Success(c, gin.H{})
}
