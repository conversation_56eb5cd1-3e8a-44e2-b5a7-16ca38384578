package operation

import (
	"context"
	"encoding/json"
	"errors"
	operation2 "marketing/internal/dao/operation"
	"marketing/internal/pkg/log"
	"sort"
	"time"

	"marketing/internal/api/operation"
	"marketing/internal/dao"
	"marketing/internal/model"
	"marketing/internal/pkg/utils"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

type ArticleCategoryInfo struct {
	Id        uint                  `json:"id"`         // 内容id
	Pid       uint                  `json:"pid"`        // 父id
	Name      string                `json:"name"`       // 内容名称
	Num       int                   `json:"num"`        // 内容数量
	Enabled   uint8                 `json:"enabled"`    // 上架发布 0:未发布 1:发布
	Order     int                   `json:"order"`      // 排序
	ChildList []ArticleCategoryInfo `json:"child_list"` // 子标签列表
}

const (
	articleCategoryCacheKey = "marketing_article_category_cache"
	articleCacheExpiration  = 24 * time.Hour
)

type OpArticleCategorySvcInterface interface {
	GetAllArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo)
	RefreshArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo)
	EditArticleCategory(c *gin.Context, id, pId int, name string, enabled, order int) error
	AddArticleCategory(c *gin.Context, pId int, name string, enabled, order, createdBy int) error
	DelArticleCategory(c *gin.Context, id int) error
}

type OpArticleCategoryService struct {
	categoryRepo operation2.OpArticleCategoryDao
	redisClient  *redis.Client
}

func NewMaterialCategoryService(categoryRepo operation2.OpArticleCategoryDao, redisClient *redis.Client) OpArticleCategorySvcInterface {
	s := &OpArticleCategoryService{
		categoryRepo: categoryRepo,
		redisClient:  redisClient,
	}

	return s
}

func (s *OpArticleCategoryService) GetAllArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo) {
	// 先尝试从Redis缓存获取
	cached, err := s.getCachedCategories(c)
	if err == nil && cached != nil {
		return *cached
	}

	// 缓存未命中，重新构建并缓存
	return s.RefreshArticleCategory(c)
}

func (s *OpArticleCategoryService) RefreshArticleCategory(c *gin.Context) (infos []ArticleCategoryInfo) {
	articleCategoryArray := make([]ArticleCategoryInfo, 0)
	categoryList, _ := s.categoryRepo.GetArticleCategoryList(c)

	for _, category := range categoryList {
		parentIds := make([]uint, 0)

		if category.ParentID != 0 {
			parentIds = s.GetArticleCategoryParentIds(category.ParentID, categoryList)
			if len(parentIds) == 0 {
				continue
			}
		}

		_ = s.addChildCategory(parentIds, &articleCategoryArray, ArticleCategoryInfo{
			Id:        category.ID,
			Pid:       category.ParentID,
			Name:      category.Name,
			Num:       int(category.NumArticles),
			Enabled:   category.Enabled,
			Order:     category.Order,
			ChildList: make([]ArticleCategoryInfo, 0),
		})
	}

	// 按ID倒序排序根级分类
	// s.sortCategoriesByIdDesc(&articleCategoryArray)

	// 缓存到Redis
	err := s.setCachedCategories(c, articleCategoryArray)
	if err != nil {
		return nil
	}

	return articleCategoryArray
}

// getCachedCategories 从Redis获取缓存的分类数据
func (s *OpArticleCategoryService) getCachedCategories(c *gin.Context) (*[]ArticleCategoryInfo, error) {
	data, err := s.redisClient.Get(context.Background(), articleCategoryCacheKey).Result()
	if err != nil {
		return nil, err
	}

	var categories []ArticleCategoryInfo
	if err := json.Unmarshal([]byte(data), &categories); err != nil {
		return nil, err
	}

	return &categories, nil
}

// setCachedCategories 将分类数据缓存到Redis
func (s *OpArticleCategoryService) setCachedCategories(c *gin.Context, categories []ArticleCategoryInfo) error {
	data, err := json.Marshal(categories)
	if err != nil {
		return err
	}

	return s.redisClient.Set(context.Background(), articleCategoryCacheKey, data, articleCacheExpiration).Err()
}

// clearCachedCategories 清除Redis缓存
func (s *OpArticleCategoryService) clearCachedCategories(c *gin.Context) error {
	return s.redisClient.Del(context.Background(), articleCategoryCacheKey).Err()
}

// sortCategoriesByIdDesc 按ID倒序排序分类（递归排序所有层级）
func (s *OpArticleCategoryService) sortCategoriesByIdDesc(categories *[]ArticleCategoryInfo) {
	if categories == nil || len(*categories) == 0 {
		return
	}

	// 排序当前层级
	sort.Slice(*categories, func(i, j int) bool {
		// 先按Order排序，如果Order相同则按ID倒序
		if (*categories)[i].Order == (*categories)[j].Order {
			return (*categories)[i].Id > (*categories)[j].Id
		}
		return (*categories)[i].Order > (*categories)[j].Order
	})

	// 递归排序子分类
	for i := range *categories {
		s.sortCategoriesByIdDesc(&(*categories)[i].ChildList)
	}
}

func (s *OpArticleCategoryService) EditArticleCategory(c *gin.Context, id, pId int, name string, enabled, order int) error {
	if len(name) == 0 {
		return errors.New("编辑内容分类:名称为空")
	}

	category := s.categoryRepo.GetArticleCategoryById(c, id)
	if category == nil {
		return errors.New("编辑内容分类:分类不存在")
	}

	uMap := make(map[string]interface{})
	uMap["name"] = name
	uMap["enabled"] = uint8(enabled)
	uMap["order"] = order

	if pId != int(category.ParentID) {
		if pId != 0 && s.categoryRepo.GetArticleCategoryById(c, pId) == nil {
			return errors.New("编辑内容分类:父标签不存在")
		}
		uMap["parent_id"] = pId
	}

	// db中修改内容分类
	dbErr := s.categoryRepo.UpdateArticleCategory(c, int(category.ID), uMap)
	if dbErr != nil {
		return dbErr
	}

	// 清除Redis缓存，下次访问时重新构建
	err := s.clearCachedCategories(c)
	if err != nil {
		log.Error("删除内容分类:清除缓存失败" + err.Error())
	}

	return nil
}

func (s *OpArticleCategoryService) AddArticleCategory(c *gin.Context, pId int, name string, enabled, order, createdBy int) error {
	if len(name) == 0 {
		return errors.New("添加内容分类:名称为空")
	}

	category := &model.OpArticleCategory{
		ParentID:  uint(pId),
		Name:      name,
		Order:     uint(order),
		Enabled:   uint(enabled),
		CreatedBy: uint(createdBy),
	}

	if pId != 0 && s.categoryRepo.GetArticleCategoryById(c, pId) == nil {
		return errors.New("添加内容分类:父标签不存在")
	}

	// db中创建内容分类
	dbErr := s.categoryRepo.CreateArticleCategory(c, category)
	if dbErr != nil {
		return dbErr
	}

	// 清除Redis缓存，下次访问时重新构建
	err := s.clearCachedCategories(c)
	if err != nil {
		log.Error("删除内容分类:清除缓存失败" + err.Error())
	}

	return nil
}

func (s *OpArticleCategoryService) DelArticleCategory(c *gin.Context, id int) error {
	// 查询内容分类
	category := s.categoryRepo.GetArticleCategoryById(c, id)
	if category == nil {
		return errors.New("删除内容分类:标签不存在")
	}

	// db中删除内容分类
	dbErr := s.categoryRepo.DeleteArticleCategory(c, id)
	if dbErr != nil {
		return dbErr
	}

	// 清除Redis缓存，下次访问时重新构建
	err := s.clearCachedCategories(c)
	if err != nil {
		log.Error("删除内容分类:清除缓存失败" + err.Error())
	}

	return nil
}

func (s *OpArticleCategoryService) addChildCategory(parentIds []uint, categoryArray *[]ArticleCategoryInfo, category ArticleCategoryInfo) int {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				num := s.addChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, category)
				(*categoryArray)[i].Num += num
				return num
			}
		}

		parentCategory := ArticleCategoryInfo{
			Id:        parentIds[0],
			ChildList: make([]ArticleCategoryInfo, 0),
		}

		// 加入子标签
		parentCategory.Num = s.addChildCategory(parentIds[1:], &parentCategory.ChildList, category)
		// 加入父标签
		*categoryArray = append(*categoryArray, parentCategory)

		return parentCategory.Num
	}

	noExist := true
	num := 0

	for i, c := range *categoryArray {
		if c.Id == category.Id {
			oldNum := (*categoryArray)[i].Num
			(*categoryArray)[i].Name = category.Name
			(*categoryArray)[i].Enabled = category.Enabled
			(*categoryArray)[i].Order = category.Order
			(*categoryArray)[i].Num = category.Num
			for _, childCategory := range (*categoryArray)[i].ChildList {
				(*categoryArray)[i].Num += childCategory.Num
			}

			num = (*categoryArray)[i].Num - oldNum
			noExist = false

			break
		}
	}

	if noExist {
		num = category.Num
		*categoryArray = append(*categoryArray, category)
	}

	// 按ID倒序排序
	sort.Slice(*categoryArray, func(i, j int) bool {
		// 先按Order排序升序，如果Order相同则按ID升序
		if (*categoryArray)[i].Order == (*categoryArray)[j].Order {
			return (*categoryArray)[i].Id < (*categoryArray)[j].Id
		}
		return (*categoryArray)[i].Order < (*categoryArray)[j].Order
	})

	return num
}

func (s *OpArticleCategoryService) delChildCategory(parentIds []uint, categoryArray *[]ArticleCategoryInfo, id uint) {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				s.delChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, id)
			}
		}
	}

	childList := make([]ArticleCategoryInfo, 0)
	for _, a := range *categoryArray {
		if a.Id != id {
			childList = append(childList, a)
		}
	}

	*categoryArray = childList

	return
}

func (s *OpArticleCategoryService) getChildCategory(parentIds []uint, categoryArray *[]ArticleCategoryInfo, id uint) []ArticleCategoryInfo {
	if len(parentIds) > 0 {
		for i, c := range *categoryArray {
			if c.Id == parentIds[0] {
				return s.getChildCategory(parentIds[1:], &(*categoryArray)[i].ChildList, id)
			}
		}
	}

	for _, c := range *categoryArray {
		if c.Id == id {
			return c.ChildList
		}
	}

	return make([]ArticleCategoryInfo, 0)
}

func (s *OpArticleCategoryService) GetArticleCategoryParentIds(pid uint, categoryList []operation.ArticleCategoryListRspItem) []uint {
	parentIds := make([]uint, 0)

	// 获取父类id
	s.getArticleCategoryParentIds(pid, categoryList, &parentIds)
	if len(parentIds) == 0 {
		return parentIds
	}

	// 因为获取的父id是从下到上,所以需要倒序一次
	utils.ReverseUintArray(&parentIds)

	return parentIds
}

func (s *OpArticleCategoryService) getArticleCategoryParentIds(pid uint, categoryList []operation.ArticleCategoryListRspItem, parentIds *[]uint) {
	for _, category := range categoryList {
		if category.ID == pid {
			// 检查父标签id是否存在回路
			if utils.UintHas(category.ID, *parentIds) {
				*parentIds = make([]uint, 0)
				return
			}

			*parentIds = append(*parentIds, category.ID)

			if category.ParentID != 0 {
				s.getArticleCategoryParentIds(category.ParentID, categoryList, parentIds)
			}

			return
		}
	}

	*parentIds = make([]uint, 0)
}

func (s *OpArticleCategoryService) GetArticleCategoryParentIdsFromDb(id uint) ([]uint, error) {
	if id == 0 {
		return nil, nil
	}

	parentIds := make([]uint, 0)

	// 获取父类id
	err := s.getArticleCategoryParentIdsFromDb(id, &parentIds)
	if err != nil {
		return nil, err
	}

	// 因为获取的父id是从下到上,所以需要倒序一次
	utils.ReverseUintArray(&parentIds)

	return parentIds, nil
}

func (s *OpArticleCategoryService) getArticleCategoryParentIdsFromDb(id uint, parentIds *[]uint) error {
	category := s.categoryRepo.GetArticleCategoryById(new(gin.Context), int(id))
	if category == nil {
		*parentIds = make([]uint, 0)
		return errors.New("标签不存在")
	}

	// 检查父标签id是否存在回路
	if utils.UintHas(category.ID, *parentIds) {
		*parentIds = make([]uint, 0)
		return errors.New("父标签异常")
	}

	*parentIds = append(*parentIds, category.ID)

	if category.ParentID != 0 {
		err := s.getArticleCategoryParentIdsFromDb(category.ParentID, parentIds)
		if err != nil {
			*parentIds = make([]uint, 0)
			return err
		}
	}

	return nil
}

// 换成redis缓存 这里不用了
func (s *OpArticleCategoryService) addVersionNum() {
	// 修改db中的版本号并清除Redis缓存
	dao.AddCacheVersion(dao.CvArticleCategory)
}
