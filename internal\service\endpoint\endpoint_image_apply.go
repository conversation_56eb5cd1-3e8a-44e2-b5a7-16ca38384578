package endpoint

import (
	"encoding/json"
	"errors"
	"fmt"
	api "marketing/internal/api/endpoint"
	adminUserdao "marketing/internal/dao/admin_user"
	dao "marketing/internal/dao/endpoint"
	appError "marketing/internal/pkg/errors"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type ImageService interface {
	Lists(c *gin.Context, param *api.GetEndpointImageReq) ([]*api.ListEndpointImageRes, int64, error)
	Audit(c *gin.Context, req api.AuditImageReq) error
}

type image struct {
	db               *gorm.DB
	endpointDao      dao.EndpointDao
	endpointImageDao dao.ImageDao
	adminUserDao     adminUserdao.UserDao
}

func NewEndpointImageService(db *gorm.DB, endpointDao dao.EndpointDao, endpointImageDao dao.ImageDao, adminUserDao adminUserdao.UserDao) ImageService {
	return &image{
		db:               db,
		endpointDao:      endpointDao,
		endpointImageDao: endpointImageDao,
		adminUserDao:     adminUserDao,
	}
}

// Lists 获取形象更新列表
func (i *image) Lists(c *gin.Context, param *api.GetEndpointImageReq) ([]*api.ListEndpointImageRes, int64, error) {
	var list []*api.ListEndpointImageRes
	var total int64

	// 如果 param.Month 为空，设置为当前年月
	if param.Month == "" {
		currentTime := time.Now()
		param.Month = currentTime.Format("2006-01")
	}

	// 构建核销时间子查询，匹配PHP逻辑
	writeOffSubQuery := `
		SELECT
			a.add_to_endpoint_id as endpoint_id,
			MAX(a.write_off_time) as write_off_time,
			COUNT(i.id) as num_installments,
			COUNT(IF(i.support_status = 2, 1, NULL)) num_suspend,
			COUNT(IF(i.recorded_at IS NOT NULL, 1, NULL)) num_processed
		FROM endpoint_application a
		LEFT JOIN endpoint_application_installment i ON a.id = i.application_id
		WHERE a.apply_type = 'new'
			AND ((a.status = 6 AND i.id IS NOT NULL) OR (a.status = 5 AND i.id IS NULL))
			AND a.add_to_endpoint_id > 0
		GROUP BY a.add_to_endpoint_id
	`

	// 构建主查询，匹配PHP的复杂JOIN逻辑
	query := i.db.WithContext(c).Table("endpoint e").
		Select(`e.id, ta.name as top_agency_name, IFNULL(sa.name, "直营") as second_agency_name,
			e.name, e.address, e.channel_level, e.code, e.created_at as endpoint_time,
			ba.write_off_time, a.id as application_id, a.created_at as updated_at, a.status,
			a.storefront, a.surroundings, a.product_image, a.product_experience, a.culture_wall,
			a.cashier, a.rest_area, a.spare_parts, a.books_borrow, a.training_room, a.other,
			a.longitude, a.latitude, a.old_longitude, a.old_latitude, a.manual_status,
			a.manual_rejected_reason, a.audit_user_id, a.audit_time, a.manual_audit_time,
			e.top_agency, e.second_agency`).
		Joins("LEFT JOIN agency ta ON e.top_agency = ta.id").
		Joins("LEFT JOIN agency sa ON e.second_agency = sa.id").
		Joins("LEFT JOIN endpoint_image_apply a ON a.endpoint_id = e.id" +
			func() string {
				if param.Month != "" {
					year := cast.ToInt(param.Month[:4])
					month := cast.ToInt(param.Month[5:7])
					return fmt.Sprintf(" AND a.year = %d AND a.month = %d", year, month)
				}
				return ""
			}()).
		Joins("LEFT JOIN (" + writeOffSubQuery + ") ba ON e.id = ba.endpoint_id")

	// 匹配PHP的WHERE条件：(e.status = 1 OR a.id IS NOT NULL)
	query = query.Where("(e.status = ? OR a.id IS NOT NULL)", 1)
	// Apply filters - 匹配PHP逻辑
	if param.Name != "" {
		query = query.Where("e.name LIKE ?", "%"+param.Name+"%")
	}
	if param.Code != "" {
		query = query.Where("e.code LIKE ?", "%"+param.Code+"%")
	}
	if param.TopAgency != 0 {
		query = query.Where("e.top_agency = ?", param.TopAgency)
	}
	if param.SecondAgency != 0 {
		// 匹配PHP逻辑：-1表示直营（second_agency = 0）
		if param.SecondAgency == 1 { // 假设前端传1表示直营
			query = query.Where("e.second_agency = ?", 0)
		} else {
			query = query.Where("e.second_agency = ?", param.SecondAgency)
		}
	}
	if param.Status != "" {
		if param.Status == "updated" {
			query = query.Where("a.id IS NOT NULL")
		} else {
			query = query.Where("a.id IS NULL")
		}
	}
	// 建店时间过滤
	if param.CreatedAtStart != "" {
		query = query.Where("e.created_at >= ?", param.CreatedAtStart)
	}
	if param.CreatedAtEnd != "" {
		query = query.Where("e.created_at <= ?", param.CreatedAtEnd)
	}

	// 核销状态过滤 - 匹配PHP逻辑
	if param.WriteOffStatus != "" {
		switch param.WriteOffStatus {
		case "not_write":
			query = query.Where("(ba.num_installments > 0 AND ba.num_suspend = 0 AND ba.num_processed = 0)")
		case "writing":
			query = query.Where("(ba.num_installments > 0 AND ba.num_suspend = 0 AND ba.num_processed > 0 AND ba.num_processed < ba.num_installments)")
		case "written":
			query = query.Where("(ba.num_installments = 0 OR ba.num_suspend > 0 OR ba.num_processed = ba.num_installments)")
		}
	}

	if param.AuditStatus != "" {
		query = query.Where(func(db *gorm.DB) *gorm.DB {
			if param.AuditStatus == "manual_to_audit" {
				return db.Where("a.manual_status = ?", "to_audit").
					Where("a.status = ?", "approved")
			}
			if strings.HasPrefix(param.AuditStatus, "manual_") {
				return db.Where("a.manual_status = ?", strings.TrimPrefix(param.AuditStatus, "manual_"))
			}
			return db.Where("a.status = ?", param.AuditStatus)
		})
	}

	// Get the total count
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (param.Page - 1) * param.PageSize

	// 构建排序条件，完全匹配PHP逻辑
	orderClause := "a.created_at desc" // 默认排序：按申请创建时间降序
	if param.OrderBy != "" {
		direction := "desc"
		if param.Order == "asc" {
			direction = "asc"
		}

		switch param.OrderBy {
		case "write_off_time":
			orderClause = "ba.write_off_time " + direction // 匹配PHP的ba.write_off_time
		case "updated_at":
			orderClause = "a.created_at " + direction // 匹配PHP的a.created_at（注意PHP中updated_at对应的是a.created_at）
		default:
			// 保持默认排序
		}
	}

	query = query.Offset(offset).Limit(param.PageSize).Order(orderClause)

	// Execute the query - 直接扫描到新的结构体
	type QueryResult struct {
		ID                    uint             `gorm:"column:id"`
		TopAgencyName         string           `gorm:"column:top_agency_name"`
		SecondAgencyName      string           `gorm:"column:second_agency_name"`
		Name                  string           `gorm:"column:name"`
		Address               string           `gorm:"column:address"`
		ChannelLevel          uint8            `gorm:"column:channel_level"`
		Code                  string           `gorm:"column:code"`
		EndpointTime          types.CustomTime `gorm:"column:endpoint_time"`
		WriteOffTime          *types.CustomTime `gorm:"column:write_off_time"`
		ApplicationID         *uint            `gorm:"column:application_id"`
		UpdatedAt             *types.CustomTime `gorm:"column:updated_at"`
		Status                *string          `gorm:"column:status"`
		ManualStatus          *string          `gorm:"column:manual_status"`
		ManualRejectedReason  *string          `gorm:"column:manual_rejected_reason"`
		AuditUserID           *uint            `gorm:"column:audit_user_id"`
		AuditTime             *types.CustomTime `gorm:"column:audit_time"`
		ManualAuditTime       *types.CustomTime `gorm:"column:manual_audit_time"`
		Longitude             *float64         `gorm:"column:longitude"`
		Latitude              *float64         `gorm:"column:latitude"`
		OldLongitude          *float64         `gorm:"column:old_longitude"`
		OldLatitude           *float64         `gorm:"column:old_latitude"`
		Storefront            *string          `gorm:"column:storefront"`
		Surroundings          *string          `gorm:"column:surroundings"`
		ProductImage          *string          `gorm:"column:product_image"`
		ProductExperience     *string          `gorm:"column:product_experience"`
		CultureWall           *string          `gorm:"column:culture_wall"`
		Cashier               *string          `gorm:"column:cashier"`
		RestArea              *string          `gorm:"column:rest_area"`
		SpareParts            *string          `gorm:"column:spare_parts"`
		BooksBorrow           *string          `gorm:"column:books_borrow"`
		TrainingRoom          *string          `gorm:"column:training_room"`
		Other                 *string          `gorm:"column:other"`
		TopAgency             int              `gorm:"column:top_agency"`
		SecondAgency          int              `gorm:"column:second_agency"`
	}

	var queryResults []QueryResult
	err = query.Scan(&queryResults).Error
	if err != nil {
		return nil, 0, err
	}

	// 收集需要查询的审核用户ID
	auditUserIdsMap := make(map[uint]struct{})
	for _, v := range queryResults {
		if v.AuditUserID != nil && *v.AuditUserID > 0 {
			auditUserIdsMap[*v.AuditUserID] = struct{}{}
		}
	}

	// 将 map 的键转换为切片
	var auditUserIds []uint
	for id := range auditUserIdsMap {
		auditUserIds = append(auditUserIds, id)
	}
	// 获取审核用户信息
	users, _ := i.adminUserDao.GetByIDs(c, auditUserIds)

	// 转换数据到最终响应格式
	list = make([]*api.ListEndpointImageRes, len(queryResults))
	for i, result := range queryResults {
		item := &api.ListEndpointImageRes{
			ID:                   result.ID,
			TopAgencyName:        result.TopAgencyName,
			SecondAgencyName:     result.SecondAgencyName,
			Name:                 result.Name,
			Address:              result.Address,
			ChannelLevel:         result.ChannelLevel,
			Code:                 result.Code,
			EndpointTime:         result.EndpointTime,
			WriteOffTime:         result.WriteOffTime,
			ApplicationID:        result.ApplicationID,
			UpdatedAt:            result.UpdatedAt,
			Status:               result.Status,
			ManualStatus:         result.ManualStatus,
			ManualRejectedReason: result.ManualRejectedReason,
			AuditUserID:          result.AuditUserID,
			AuditTime:            result.AuditTime,
			ManualAuditTime:      result.ManualAuditTime,
			Longitude:            result.Longitude,
			Latitude:             result.Latitude,
			OldLongitude:         result.OldLongitude,
			OldLatitude:          result.OldLatitude,
			Storefront:           result.Storefront,
			Surroundings:         result.Surroundings,
			ProductImage:         result.ProductImage,
			ProductExperience:    result.ProductExperience,
			CultureWall:          result.CultureWall,
			Cashier:              result.Cashier,
			RestArea:             result.RestArea,
			SpareParts:           result.SpareParts,
			BooksBorrow:          result.BooksBorrow,
			TrainingRoom:         result.TrainingRoom,
			Other:                result.Other,
			TopAgency:            result.TopAgency,
			SecondAgency:         result.SecondAgency,
		}

		// 计算距离
		if result.OldLatitude != nil && result.OldLongitude != nil &&
		   result.Latitude != nil && result.Longitude != nil {
			distance := utils.Distance(*result.OldLatitude, *result.OldLongitude,
				*result.Latitude, *result.Longitude, 1, 1)
			distanceStr := fmt.Sprintf("%.0f米", distance)
			item.Distance = &distanceStr
		}

		// 设置审核用户名
		if result.AuditUserID != nil && *result.AuditUserID > 0 {
			for _, user := range users {
				if user.ID == *result.AuditUserID {
					item.AuditUsername = &user.Name
					break
				}
			}
		}

		list[i] = item
	}

	return list, total, nil
}

func (i *image) Audit(c *gin.Context, req api.AuditImageReq) error {
	//查询id申请是否存在
	existApply, err := i.endpointImageDao.GetByID(c, req.ID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return appError.NewErr("申请不存在")
	}
	if err != nil {
		return err
	}
	if existApply.ManualStatus != "to_audit" {
		//return appError.NewErr("形象申请已经审核")
	}
	updateData := map[string]interface{}{
		"manual_status":          req.Status,
		"audit_user_id":          c.GetUint("uid"),
		"manual_audit_time":      time.Now(),
		"manual_rejected_reason": req.AuditOpinion,
	}
	// 事务
	err = i.db.Transaction(func(tx *gorm.DB) error {
		if err := i.endpointImageDao.Update(c, req.ID, updateData); err != nil {
			return err
		}
		if req.Status == "approved" {
			// Update endpoint images if approved
			// Add your logic here to update the endpoint images
			var images []string

			// 直接将所有字段的数组合并
			arrays := []types.JSONStringArray{
				existApply.ProductImage,
				existApply.ProductExperience,
				existApply.CultureWall,
				existApply.Cashier,
				existApply.RestArea,
				existApply.SpareParts,
				existApply.BooksBorrow,
				existApply.TrainingRoom,
				existApply.Surroundings,
				existApply.Other,
			}

			// 处理每个字段
			for _, arr := range arrays {
				images = append(images, []string(arr)...)
			}

			// 如果需要将结果压缩成 JSON
			compressedImages, err := json.Marshal(images)
			if err != nil {
				return err
			}
			//更新终端形象
			existEndpoint, err := i.endpointDao.GetEndpointByID(c, existApply.EndpointID)
			if err != nil {
				return err
			}
			compressedImagesStr := string(compressedImages)
			existEndpoint.Images = &compressedImagesStr
			if _, err = i.endpointDao.UpdateEndpoint(c, existEndpoint); err != nil {
				return err
			}
		}
		return nil
	})
	return err
}
