package operation

import (
	"errors"
	"marketing/internal/api/operation"
	articleDao "marketing/internal/dao/operation"
	"marketing/internal/pkg/types"

	"github.com/gin-gonic/gin"
)

type OpPublisherSvcInterface interface {
	GetOpPublisherList(c *gin.Context, pageNum, pageSize int) (list []operation.OpPublisherInfo, total int64)
	EditOpPublisher(c *gin.Context, id int, name, avatar string, userIds []int) error
	DeleteOpPublisher(c *gin.Context, id int) error
}

type OpPublisherService struct {
	opPublisherDao articleDao.OpPublisherDao
}

func NewOpPublisherService(opPublisherDao articleDao.OpPublisherDao) OpPublisherSvcInterface {
	return &OpPublisherService{
		opPublisherDao: opPublisherDao,
	}
}

func (s *OpPublisherService) GetOpPublisherList(c *gin.Context, pageNum, pageSize int) (infos []operation.OpPublisherInfo, total int64) {
	list, total := s.opPublisherDao.GetOpPublisherList(c, pageNum, pageSize)

	pIds := make([]uint, 0)
	for _, l := range list {
		pIds = append(pIds, l.ID)
	}

	publisherUserList := s.opPublisherDao.GetOpPublisherUser(c, pIds)

	articleInfos := s.opPublisherDao.GetOpPublisherArticleInfo(c, pIds)

	for _, l := range list {
		info := operation.OpPublisherInfo{
			Id:         l.ID,
			Name:       l.Name,
			Avatar:     types.OssPath(l.Avatar),
			AuthorList: make([]operation.OpPublisherUserInfo, 0),
		}

		for _, u := range publisherUserList {
			if l.ID == u.PublisherId {
				info.AuthorList = append(info.AuthorList, u)
			}
		}

		for _, a := range articleInfos {
			if l.ID == a.PublisherId {
				info.ArticleNum = int(a.Num)
				break
			}
		}

		infos = append(infos, info)
	}

	return
}

func (s *OpPublisherService) EditOpPublisher(c *gin.Context, id int, name, avatar string, userIds []int) error {
	if len(name) == 0 {
		return errors.New("名称为空")
	}

	if len(avatar) == 0 {
		return errors.New("请上传头像")
	}

	hasUserIds := s.opPublisherDao.CheckAdminUserIs(c, userIds)

	if id > 0 {
		return s.opPublisherDao.EditOpPublisher(c, id, name, avatar, hasUserIds)
	} else {
		return s.opPublisherDao.CreateOpPublisher(c, name, avatar, hasUserIds)
	}
}

func (s *OpPublisherService) DeleteOpPublisher(c *gin.Context, id int) error {
	return s.opPublisherDao.DeleteOpPublisher(c, id)
}
