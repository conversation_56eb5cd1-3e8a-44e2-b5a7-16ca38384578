package utils

import (
	"encoding/json"
	"strings"

	"github.com/spf13/viper"
)

// UrlImageTrans 数据库图片转换
func UrlImageTrans(url string) ([]string, error) {
	if url == "" {
		return []string{}, nil
	}
	var address []string
	err := json.Unmarshal([]byte(url), &address)
	if err != nil {
		return nil, err // 发生错误时返回错误
	}

	// 如果地址切片为空，直接返回空的地址切片和nil错误
	if len(address) == 0 {
		return []string{}, nil
	}

	// 为每个地址添加相应的前缀
	for i, v := range address {
		address[i] = AddPrefix(v)
	}

	return address, nil
}

// SliceTrans 切片转化
func SliceTrans(url []string) string {
	if len(url) == 0 {
		return ""
	}
	for k, v := range url {
		url[k] = DeletePrefix(v)
	}
	str, err := json.Marshal(url)
	if err != nil {
		return ""
	}
	return string(str)
}

// AddPrefix 根据url内容添加相应前缀
func AddPrefix(url string) string {
	OldPrefix := viper.GetString("oss.oldUrl")
	NewPrefix := viper.GetString("oss.url")
	if url == "" {
		return url
	}
	if strings.HasPrefix(url, "https://") || strings.HasPrefix(url, "http://") {
		return url
	}
	if strings.HasPrefix(url, "rbcare") {
		return OldPrefix + url
	}
	return NewPrefix + url
}

// DeletePrefix 删除url中的前缀
func DeletePrefix(url string) string {
	OldPrefix := viper.GetString("oss.oldUrl")
	NewPrefix := viper.GetString("oss.url")
	if url == "" {
		return url
	}
	if strings.HasPrefix(url, OldPrefix) {
		return strings.TrimPrefix(url, OldPrefix)
	}
	if strings.HasPrefix(url, NewPrefix) {
		return strings.TrimPrefix(url, NewPrefix)
	}
	return url
}
