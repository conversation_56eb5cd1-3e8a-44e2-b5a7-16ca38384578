package endpoint

import (
	"marketing/internal/api"
	"marketing/internal/pkg/types"
)

// GetEndpointImageReq 查询终端角色入参
type GetEndpointImageReq struct {
	api.PaginationParams
	Month           string `json:"month" form:"month" binding:"required"`
	Name            string `json:"name" form:"name"`                       // 终端名称
	Code            string `json:"code" form:"code"`                       // 终端编码
	TopAgency       uint   `json:"top_agency" form:"top_agency"`           // 一级代理商名称
	SecondAgency    uint   `json:"second_agency" form:"second_agency"`     // 二级代理商名称
	Status          string `json:"status" form:"status"`                   // 终端状态，updated 为更新，not_updated 为未更新
	WriteOffStatus  string `json:"write_off_status" form:"write_off_status"` // 核销状态: not_write, writing, written
	CreatedAtStart  string `json:"created_at_start" form:"created_at_start"` // 建店开始时间
	CreatedAtEnd    string `json:"created_at_end" form:"created_at_end"`     // 建店结束时间
	// manual_to_audit: 人工待审, manual_approved: 人工审核通过, manual_rejected: 人工审核不通过, approved: 自动审核通过, rejected: 自动审核不通过
	AuditStatus string `json:"audit_status" form:"audit_status"`
	OrderBy     string `json:"order_by" form:"order_by"`           // 排序字段: write_off_time, updated_at
	Order       string `json:"order" form:"order"`                 // 排序方向: asc, desc
}

type ListEndpointImageRes struct {
	ID                    uint             `json:"id"`                      // 终端ID
	TopAgencyName         string           `json:"top_agency"`              // 一级代理名称
	SecondAgencyName      string           `json:"second_agency"`           // 二级代理名称
	Name                  string           `json:"name"`                    // 终端名称
	Address               string           `json:"address"`                 // 终端地址
	ChannelLevel          uint8            `json:"channel_level"`           // 渠道等级
	Code                  string           `json:"code"`                    // 终端编码
	EndpointTime          types.CustomTime `json:"created_at"`              // 建店时间
	WriteOffTime          *types.CustomTime `json:"write_off_time"`         // 核销时间
	ApplicationID         *uint            `json:"application_id"`          // 申请ID
	UpdatedAt             *types.CustomTime `json:"updated_at"`             // 更新时间
	Status                *string          `json:"status"`                  // 自动审核状态
	ManualStatus          *string          `json:"manual_status"`           // 人工审核状态
	ManualRejectedReason  *string          `json:"manual_rejected_reason"`  // 人工审核拒绝原因
	AuditUserID           *uint            `json:"audit_user_id"`           // 审核用户ID
	AuditTime             *types.CustomTime `json:"audit_time"`             // 审核时间
	ManualAuditTime       *types.CustomTime `json:"manual_audit_time"`      // 人工审核时间
	AuditUsername         *string          `json:"audit_username"`          // 审核用户名
	Longitude             *float64         `json:"longitude"`               // 经度
	Latitude              *float64         `json:"latitude"`                // 纬度
	OldLongitude          *float64         `json:"old_longitude"`           // 原经度
	OldLatitude           *float64         `json:"old_latitude"`            // 原纬度
	Distance              *string          `json:"distance"`                // 距离
	// 图片字段
	Storefront        *string `json:"storefront"`         // 门头
	Surroundings      *string `json:"surroundings"`       // 周边环境
	ProductImage      *string `json:"product_image"`      // 产品列区
	ProductExperience *string `json:"product_experience"` // 产品体验区
	CultureWall       *string `json:"culture_wall"`       // 文化墙
	Cashier           *string `json:"cashier"`            // 收银区
	RestArea          *string `json:"rest_area"`          // 休息区
	SpareParts        *string `json:"spare_parts"`        // 礼品配件区
	BooksBorrow       *string `json:"books_borrow"`       // 图书借阅区
	TrainingRoom      *string `json:"training_room"`      // 培训教室
	Other             *string `json:"other"`              // 其它
	// 内部字段，用于处理
	TopAgency    int `json:"-"`
	SecondAgency int `json:"-"`
}

type AuditImageReq struct {
	ID           uint   `json:"id"`
	Status       string `json:"status" binding:"required,oneof=approved rejected"`
	AuditOpinion string `json:"audit_opinion" binding:"required_if=Status rejected,max=100"`
}
