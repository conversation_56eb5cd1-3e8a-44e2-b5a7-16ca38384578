package e

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

func ReqParamStr(c *gin.Context, param string, def ...string) string {
	v := c.Query(param)
	if v == "" {
		v = c.PostForm(param)
	}
	if len(def) > 0 && v == "" {
		v = def[0]
	}
	return v
}

func ReqParamInt(c *gin.Context, param string, def ...int) int {
	v := c.Query(param)
	if v == "" {
		v = c.PostForm(param)
	}
	i, err := strconv.Atoi(v)
	if err != nil && len(def) > 0 {
		return def[0]
	}
	return i
}

func ReqParamInt64(c *gin.Context, param string, def ...int64) int64 {
	v := c.Query(param)
	if v == "" {
		v = c.PostForm(param)
	}
	i, err := strconv.ParseInt(v, 10, 64)
	if err != nil && len(def) > 0 {
		return def[0]
	}
	return i
}

func ReqParamFloat32(c *gin.Context, param string, def ...float32) float32 {
	v := c.Query(param)
	if v == "" {
		v = c.PostForm(param)
	}
	i, err := strconv.ParseFloat(v, 32)
	if err != nil && len(def) > 0 {
		return def[0]
	}
	return float32(i)
}

func ReqParamFloat64(c *gin.Context, param string, def ...float64) float64 {
	v := c.Query(param)
	if v == "" {
		v = c.PostForm(param)
	}
	i, err := strconv.ParseFloat(v, 64)
	if err != nil && len(def) > 0 {
		return def[0]
	}
	return i
}
